version: '3.8'

services:
  luno-mcp-server:
    build: .
    container_name: luno-mcp-server
    ports:
      - "8000:8000"
    environment:
      # Luno API credentials (required for private endpoints)
      LUNO_API_KEY: ${LUNO_API_KEY}
      LUNO_API_SECRET: ${LUNO_API_SECRET}
      
      # FastMCP server configuration
      MCP_TRANSPORT: streamable-http
      MCP_HOST: 0.0.0.0
      MCP_PORT: 8000
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
