---
layout: default
title: "Luno MCP Server Documentation"
---

# Luno MCP Server

A Model Context Protocol (MCP) server for seamless integration with the Luno cryptocurrency exchange. This server provides comprehensive tools for trading, market analysis, and account management through the MCP framework.

## 🚀 Features

- **Real-time Market Data**: Get current prices, market overviews, and trading pair information
- **Trading Operations**: Place orders, cancel orders, and check order status
- **Account Management**: View balances and transaction history
- **Historical Data**: Access OHLC candlestick data and price range analysis
- **Fee Information**: Get trading fees for specific pairs

## 📋 Quick Start

1. **Installation**: Clone the repository and install dependencies
2. **Configuration**: Set up your Luno API credentials
3. **Integration**: Connect with Claude Desktop or other MCP-compatible clients

```bash
# Clone the repository
git clone https://github.com/amanasmuei/mcp-luno.git
cd mcp-luno

# Install dependencies
pip install -r requirements.txt

# Configure your API keys
cp .env.example .env
# Edit .env with your Luno API credentials
```

## 📚 Documentation

### Setup Guides
- [Claude <PERSON>op Setup](CLAUDE_DESKTOP_SETUP)
- [Quick Fix Guide](QUICK_FIX_CLAUDE_DESKTOP)
- [GitHub Pages Fix](GITHUB_PAGES_FIX)

### Advanced Guides
- [Historical Data Guide](HISTORICAL_DATA_GUIDE)
- [Migration Guide](MIGRATION)
- [Working Solution](WORKING_SOLUTION)
- [Final FastMCP Solution](FINAL_FASTMCP_SOLUTION)

## 💝 Support

- [Donation Setup](DONATION_SETUP)
- [Donate Now](donate.html)

## 🔗 Repository

Visit the [GitHub repository](https://github.com/amanasmuei/mcp-luno) for the latest updates and to contribute to the project.

---

*Built with ❤️ for the cryptocurrency trading community*