# Luno API Configuration
# Get your API credentials from: https://www.luno.com/wallet/security/api_keys
LUNO_API_KEY=your_api_key_here
LUNO_API_SECRET=your_api_secret_here

# Server Configuration
LUNO_MCP_SERVER_NAME=luno-mcp-server
LUNO_MCP_SERVER_DESCRIPTION=MCP server for Luno cryptocurrency exchange API

# Transport Configuration
LUNO_MCP_TRANSPORT=stdio
LUNO_MCP_HOST=localhost
LUNO_MCP_PORT=8000

# Logging Configuration
LUNO_MCP_LOG_LEVEL=INFO

# API Configuration
LUNO_MCP_API_BASE_URL=https://api.luno.com
LUNO_MCP_REQUEST_TIMEOUT=30.0
LUNO_MCP_MAX_REQUESTS_PER_MINUTE=60

# Alternative environment variable names (legacy support)
MCP_TRANSPORT=stdio
MCP_HOST=localhost
MCP_PORT=8000
LOG_LEVEL=INFO
